<template>
  <h3>
    <span class="left-bar" />
    <slot>
      {{ $route.meta.title }}
    </slot>
  </h3>
</template>

<script>
export default {
  name: 'CommonHeader',
  components: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  methods: {}
}
</script>

<style lang="less" scoped>
// .commom-header {
  h3 {
    display: flex;
    align-items: center;
    margin-top: 0;
    // padding-left: 20px;
  }
  .left-bar {
    background:rgba(76,175,80,1);
    display: inline-block;
    height:20px;
    width:5px;
    margin-right: 10px;
    border-radius: 3px;
    opacity: 0.5;
  }
// }
</style>
