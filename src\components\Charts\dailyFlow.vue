<template>
  <div>
    <div
      :id="id"
      :class="className"
      :style="{ height: height, width: width }"
    />
  </div>
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

export default {
  name: 'DailyFlow',
  components: {},
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    propData: {
      type: Array,
      default: () => []
    },
    dayCount: {
      type: Array,
      default: () => []
    },
    timeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {},
  watch: {
    timeData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  created() {},
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption({
        grid: {
          top: '20%',
          left: '6%',
          right: '1%',
          bottom: '0%',
          containLabel: true
        },
        legend: {
          data: ['目标取水量', '实际取水量'],
          show: false,
          padding: 5,
          itemWidth: 10,
          itemHeight: 5,
          top: '3%',
          right: '0%',
          textStyle: { color: '#999999', fontSize: 10 }
        },
        tooltip: {
          trigger: 'axis',
          position: 'top'
        },
        xAxis: {
          data: [...this.timeData].map((item) => item.substring(0, 11)),
          linestyle: {
            color: '#ccc'
          },
          axisLine: {
            // 坐标轴轴线相关设置。数学上的x轴
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            // 坐标轴刻度标签的相关设置
            rotate: 45,
            textStyle: {
              color: '#ccc',
              margin: 15
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          name: '单位：万m3',
          color: '#ccc',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            color(e) {
              return '#ccc'
            },
            formatter: '{value}',
            show: true
          }
        },
        series: [
          {
            name: '目标取水量',
            type: 'line',
            smooth: true,
            abel: {
              show: true,
              position: 'top',
              textStyle: {
                color: '#fff'
              }
            },
            itemStyle: {
              normal: {
                color: '#1ED6A7'
              }
            },
            lineStyle: {
              normal: {
                color: '#1ED6A7' // 线条颜色
              }
            },
            barWidth: 8,
            data: [...this.dayCount]
          }
        ]
      })
      this.chart.hideLoading()
    }
  }
}
</script>

<style lang="scss">
</style>
