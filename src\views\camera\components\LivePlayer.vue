<template>
  <div v-loading="loading" class="video-box">
    <LivePlayer
      :videoUrl="videoUrl"
      fluent
      :hide-snapshot-button="true"
      autoplay
      live
      custom-buttons=":yuntai"
      aspect="fullscreen"
      @customButtons="customButtons"
    ></LivePlayer>
    <div class="holder-box" v-show="holderShow">
      <h2 class="title"></h2>
      <i class="close-icon" @click="holderShow = false"></i>
      <div class="content-box">
        <div
          class="btn-item left"
          @mousedown.stop="ptzControl('LEFT')"
          @mouseup.stop="onerBind"
        ></div>
        <!--        <div class="btn-item left-top" @mousedown.stop="ptzControl('upleft')" @mouseup.stop="onerBind"></div>-->
        <div
          class="btn-item top"
          @mousedown.stop="ptzControl('UP')"
          @mouseup.stop="onerBind"
        ></div>
        <!--        <div class="btn-item top-right" @mousedown.stop="ptzControl('upright')" @mouseup.stop="onerBind"></div>-->
        <div
          class="btn-item right"
          @mousedown.stop="ptzControl('RIGHT')"
          @mouseup.stop="onerBind"
        ></div>
        <!--        <div class="btn-item right-bottom" @mousedown.stop="ptzControl('downright')" @mouseup.stop="onerBind"></div>-->
        <div
          class="btn-item bottom"
          @mousedown.stop="ptzControl('DOWN')"
          @mouseup.stop="onerBind"
        ></div>
        <!--        <div class="btn-item bottom-left" @mousedown.stop="ptzControl('downleft')" @mouseup.stop="onerBind"></div>-->
      </div>
      <!--      <div class="control-item">-->
      <!--        <span>缩放</span>-->
      <!--        <div class="btn-item left" @mousedown.stop="ptzControl('zoomin')" @mouseup.stop="onerBind"></div>-->
      <!--        <div class="btn-item right" @mousedown.stop="ptzControl('zoomout')" @mouseup.stop="onerBind"></div>-->
      <!--      </div>-->
    </div>
  </div>
</template>

<script>
import LivePlayer from "@liveqing/liveplayer";
import { ptzControl } from "@/api/camera";
import { getWaterLive } from "@/api/camera";

export default {
  name: "",
  props: {
    deviceId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      videoUrl: "",
      holderShow: false, //是否显示云台控制
      loading: false
    };
  },
  created() {
    this.deviceId && this.initVideo();
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    // 播放视频
    initVideo() {
      this.loading = true;
      getWaterLive(this.deviceId)
        .then(res => {
          this.videoUrl = res.data.data.flvHttps;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 控制云台显示隐藏
    customButtons() {
      this.holderShow = !this.holderShow;
    },
    // 操控云台
    ptzControl(direction) {
      ptzControl({ channelId: this.deviceId, ptz: direction, speed: 30 }).then(
        res => {
          console.log("云台控制", res);
        }
      );
    },
    // 停止转动
    onerBind() {
      ptzControl({ channelId: this.deviceId, ptz: "STOP", speed: 30 }).then(
        res => {
          console.log("云台控制", res);
        }
      );
    }
  },
  computed: {},
  watch: {
    deviceId(val) {
      if (val) {
        this.initVideo();
      }
    }
  },
  components: { LivePlayer }
};
</script>

<style lang="scss">
.close-icon {
  position: absolute;
  top: -45px;
  right: -23px;
  z-index: 10;
  width: 62px;
  height: 61px;
  font-size: 20px;
  color: rgb(0, 233, 240);
  cursor: pointer;
  background: url("~@/assets/monitor/<EMAIL>") no-repeat center;
  background-size: contain;
}
.yuntai {
  position: relative;
  &::before {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    content: "";
    background: url("~@/assets/monitor/<EMAIL>") no-repeat center;
    background-size: contain;
    transform: translate(-50%, -50%);
  }
}

.video-box {
  // background-color: pink;
  position: relative;
  width: 100%;
  height: 100%;
  .holder-box {
    position: absolute;
    right: 0;
    bottom: 15px;
    width: 278px;
    height: 260px;
    background: url("~@/assets/monitor/<EMAIL>") no-repeat center;
    background-size: contain;
    transform: scale(0.6);
    transform-origin: bottom right;
    .title {
      width: 278px;
      height: 32px;
      background: url("~@/assets/monitor/<EMAIL>") no-repeat center;
      background-size: contain;
    }
    .close-icon {
      position: absolute;
      top: 0;
      right: 0;
      width: 32px;
      height: 33px;
      background: url("~@/assets/monitor/<EMAIL>") no-repeat center;
      background-size: contain;
    }
    .content-box {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 180px;
      height: 180px;
      background: url("~@/assets/monitor/-s-yaokong.png") no-repeat center;
      background-size: contain;
      transform: translate(-50%, -50%);
      .btn-item {
        // background-color: pink;
        position: absolute;
        width: 20px;
        height: 20px;
        cursor: pointer;
        &.left {
          top: 50%;
          left: 32px;
          transform: translateY(-50%) scale(1.67);
        }
        &.left-top {
          top: 40px;
          left: 40px;
        }
        &.top {
          top: 32px;
          left: 50%;
          transform: translateX(-50%) scale(1.67);
        }
        &.top-right {
          top: 40px;
          right: 40px;
        }
        &.right {
          top: 50%;
          right: 32px;
          transform: translateY(-50%) scale(1.67);
        }
        &.right-bottom {
          right: 40px;
          bottom: 40px;
        }
        &.bottom {
          bottom: 32px;
          left: 50%;
          transform: translateX(-50%) scale(1.67);
        }
        &.bottom-left {
          bottom: 40px;
          left: 40px;
        }
      }
    }
    .control-item {
      position: absolute;
      top: 165px;
      left: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 152px;
      height: 28px;
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
      background: url("~@/assets/monitor/<EMAIL>") no-repeat center;
      background-size: contain;
      transform: translateX(-50%);
      .btn-item {
        width: 28px;
        height: 28px;
        cursor: pointer;
        border-radius: 5px;
      }
      .left {
        position: absolute;
        top: 0;
        left: 0;
      }
      .right {
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  }
}
</style>
<style></style>
