<style lang="scss" scoped>
.no-data {
  position: relative;
  span {
    position:absolute;
    top:50%;
    left:50%;
    transform:translate(-50%,-50%);
    color: #999999;
    font-size: 20px;
  }
}
</style>
<template>
  <div
    v-if="airData.yData && airData.yData.length"
    :id="id"
    :class="className"
    :style="{height:height,width:width}"
  />
  <div
    v-else
    :style="{ height: height, width: width }"
    class="no-data"
  ><span>暂无数据</span></div>
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    airData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    airData(val) {
      if (this.chart) {
        this.chart.clear()
        this.chart = null
      }
      if (val.yData && val.yData.length) {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    }
  },
  mounted() {
    // this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      const option = {
        grid: {
          top: '15%',
          left: '3%',
          bottom: '10%',
          right: '3%'
          // containLabel: true
        },
        tooltip: {
          trigger: 'axis'
          // axisPointer: {
          //   lineStyle: {
          //     color: {
          //       type: 'linear',
          //       x: 0,
          //       y: 0,
          //       x2: 0,
          //       y2: 1,
          //       colorStops: [
          //         {
          //           offset: 0,
          //           color: 'rgba(255,255,255,0)' // 0% 处的颜色
          //         },
          //         {
          //           offset: 0.5,
          //           color: 'rgba(255,255,255,1)' // 100% 处的颜色
          //         },
          //         {
          //           offset: 1,
          //           color: 'rgba(255,255,255,0)' // 100% 处的颜色
          //         }
          //       ],
          //       global: false // 缺省为 false
          //     }
          //   }
          // }
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              formatter: '{value}',
              fontSize: 14,
              margin: 20,
              textStyle: {
                color: '#999999'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#F5F5F5'
              }
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#999999'
              }
            },
            axisTick: {
              show: false
            },
            data: this.airData.xData
          }
        ],
        yAxis: [
          {
            name: this.airData.unit ? `单位：${this.airData.unit}` || '' : '',
            boundaryGap: false,
            type: 'value',
            axisLabel: {
              textStyle: {
                color: '#999999'
              }
            },
            nameTextStyle: {
              color: '#999999',
              fontSize: 12,
              lineHeight: 40
            },
            splitLine: {
              lineStyle: {
                color: '#F5F5F5'
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#F5F5F5'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: this.airData.name || '',
            type: 'line',
            smooth: true,
            showSymbol: true,
            symbolSize: 8,
            zlevel: 1,
            itemStyle: {
              color: (params) => {
                if (this.type) {
                  return this.airData.statusList[params.dataIndex] ? 'red' : '#7EFF00'
                }
                return (params.value <= this.airData.alarmValue || this.airData.alarmValue == 0) ? '#7EFF00' : 'red'
              }
              // borderColor: '#7EFF00'
            },
            lineStyle: {
              normal: {
                width: 2,
                color: '#FF964F'
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(255, 150, 79, 0.3)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(0,0,0,0)'
                    }
                  ],
                  false
                )
              }
            },
            data: this.airData.yData
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>
