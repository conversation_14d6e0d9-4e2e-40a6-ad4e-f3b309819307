<template>
  <div
    :id="id"
    :style="{height:height,width:width}"
  />
</template>

<script>
/* eslint-disable */
import echarts from 'echarts'
import resize from '@/components/Charts/mixins/resize'

export default {
  mixins: [resize],
  props: {
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    seriesData: {
      required: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData(newValue, oldValue) {
      if (newValue) {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      const xAxisData = []
      const data = []
      const data2 = []
      for (let i = 0; i < 50; i++) {
        xAxisData.push(i)
        data.push((Math.sin(i / 5) * (i / 5 - 10) + i / 6) * 5)
        data2.push((Math.sin(i / 5) * (i / 5 + 10) + i / 6) * 3)
      }
      this.chart.setOption({
        title: {
          text: 'AQI',
          textStyle: {
            color: '#333',
            fontSize: 20
          },
          top: '30%',
          left: 'center'
        },
        series: [
          {
            startAngle: 215,
            endAngle: -35,
            min: 0,
            max: 300,
            radius: '90%',
            type: 'gauge',
            axisLine: {
              // 坐标轴线
              lineStyle: {
                // 属性lineStyle控制线条样式
                color: [
                  [0.167, 'rgb(0, 255, 0)'],
                  [0.334, 'rgb(255, 255, 0)'],
                  [0.5, 'rgb(255, 126, 0)'],
                  [0.667, 'rgb(255, 0, 0)'],
                  [0.835, 'rgb(153, 0, 76)'],
                  [1, 'rgb(126, 0, 35)']
                ],
                width: 18
              }
            },
            axisLabel: {
              // formatter: function(e) {
              //   switch (e + '') {
              //     case '50':
              //       return '优'
              //     case '100':
              //       return '良'
              //     case '150':
              //       return '轻度污染'
              //     case '200':
              //       return '中度污染'
              //     case '300':
              //       return '重度污染'
              //     case '500':
              //       return '严重污染'
              //     default:
              //       return e
              //     // case '50':
              //     //   return ''
              //     // case '150':
              //     //   return ''
              //     // case '250':
              //     //   return ''
              //     // case '350':
              //     //   return ''
              //     // case '450':
              //     //   return ''
              //     // default:
              //     //   return e
              //   }
              // },
              // 坐标轴小标记
              textStyle: {
                // 属性lineStyle控制线条样式
                color: '#333',
                shadowBlur: 5
              },
              fontSize: 12
            },
            axisTick: {
              length: 3
            },
            splitLine: {
              length: 15,
              lineStyle: {
                width: 1
              }
            },
            pointer: {
              width: 5
            },
            itemStyle: {
              color: 'red'
            },
            title: {
              show: true, // 是否显示标题,默认 true。
              offsetCenter: [0, '50%'], // 相对于仪表盘中心的偏移位置，数组第一项是水平方向的偏移，第二项是垂直方向的偏移。可以是绝对的数值，也可以是相对于仪表盘半径的百分比。
              color: '#000',
              width: '50',
              height: '20',
              padding: [4, 8],
              borderRadius: 8,
              fontSize: 14,
              backgroundColor:
                this.seriesData <= 50
                  ? 'rgb(0, 255, 0)'
                  : this.seriesData <= 100
                    ? 'rgb(255, 255, 0)'
                    : this.seriesData <= 150
                      ? 'rgb(255, 126, 0)'
                      : this.seriesData <= 200
                        ? 'rgb(255, 0, 0)'
                        : this.seriesData <= 300
                          ? 'rgb(153, 0, 76)'
                          : 'rgb(126, 0, 35)'
            },
            detail: {
              fontSize: 26,
              color: '#666666',
              offsetCenter: [0, '25%']
            },
            data: [
              {
                value: this.seriesData,
                name:
                  this.seriesData <= 50
                    ? '优'
                    : this.seriesData <= 100
                      ? '良'
                      : this.seriesData <= 150
                        ? '轻度污染'
                        : this.seriesData <= 200
                          ? '中度污染'
                          : this.seriesData <= 300
                            ? '重度污染'
                            : '严重污染'
              }
            ]
          }
        ]
      })
    }
  }
}
</script>
